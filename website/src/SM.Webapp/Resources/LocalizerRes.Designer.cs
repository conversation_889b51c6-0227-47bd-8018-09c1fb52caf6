﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SM.Webapp.Resources {
    using System;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class LocalizerRes {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal LocalizerRes() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("SM.Webapp.Resources.LocalizerRes", typeof(LocalizerRes).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        internal static string page_meta_title {
            get {
                return ResourceManager.GetString("page.meta.title", resourceCulture);
            }
        }
        
        internal static string brochure_subtitle {
            get {
                return ResourceManager.GetString("brochure.subtitle", resourceCulture);
            }
        }
        
        internal static string language {
            get {
                return ResourceManager.GetString("language", resourceCulture);
            }
        }
        
        internal static string brochure_title {
            get {
                return ResourceManager.GetString("brochure.title", resourceCulture);
            }
        }
        
        internal static string page_meta_keywords {
            get {
                return ResourceManager.GetString("page.meta.keywords", resourceCulture);
            }
        }
        
        internal static string page_meta_description {
            get {
                return ResourceManager.GetString("page.meta.description", resourceCulture);
            }
        }
        
        internal static string error_title {
            get {
                return ResourceManager.GetString("error.title", resourceCulture);
            }
        }
        
        internal static string about_title_how {
            get {
                return ResourceManager.GetString("about.title.how", resourceCulture);
            }
        }
        
        internal static string about_title_why {
            get {
                return ResourceManager.GetString("about.title.why", resourceCulture);
            }
        }
        
        internal static string contact_message_sendbutton_text {
            get {
                return ResourceManager.GetString("contact.message.sendbutton.text", resourceCulture);
            }
        }
        
        internal static string contact_subtitle {
            get {
                return ResourceManager.GetString("contact.subtitle", resourceCulture);
            }
        }
        
        internal static string contact_title {
            get {
                return ResourceManager.GetString("contact.title", resourceCulture);
            }
        }
        
        internal static string contact_work_hour {
            get {
                return ResourceManager.GetString("contact.work.hour", resourceCulture);
            }
        }
        
        internal static string products_filter_hide {
            get {
                return ResourceManager.GetString("products.filter.hide", resourceCulture);
            }
        }
        
        internal static string products_filter_show {
            get {
                return ResourceManager.GetString("products.filter.show", resourceCulture);
            }
        }
        
        internal static string products_short_az {
            get {
                return ResourceManager.GetString("products.short.az", resourceCulture);
            }
        }
        
        internal static string products_short_za {
            get {
                return ResourceManager.GetString("products.short.za", resourceCulture);
            }
        }
        
        internal static string products_sorting {
            get {
                return ResourceManager.GetString("products.sorting", resourceCulture);
            }
        }
        
        internal static string product_detail_dimension_desc {
            get {
                return ResourceManager.GetString("product.detail.dimension.desc", resourceCulture);
            }
        }
        
        internal static string product_detail_dimension_title {
            get {
                return ResourceManager.GetString("product.detail.dimension.title", resourceCulture);
            }
        }
        
        internal static string product_detail_handmadeicon {
            get {
                return ResourceManager.GetString("product.detail.handmadeicon", resourceCulture);
            }
        }
        
        internal static string product_detail_howtoapply {
            get {
                return ResourceManager.GetString("product.detail.howtoapply", resourceCulture);
            }
        }
        
        internal static string product_detail_howtoapply_desc {
            get {
                return ResourceManager.GetString("product.detail.howtoapply.desc", resourceCulture);
            }
        }
        
        internal static string product_detail_ingredients {
            get {
                return ResourceManager.GetString("product.detail.ingredients", resourceCulture);
            }
        }
        
        internal static string product_detail_skinmoisture {
            get {
                return ResourceManager.GetString("product.detail.skinmoisture", resourceCulture);
            }
        }
        
        internal static string product_detail_warning {
            get {
                return ResourceManager.GetString("product.detail.warning", resourceCulture);
            }
        }
        
        internal static string product_detail_warning_title {
            get {
                return ResourceManager.GetString("product.detail.warning.title", resourceCulture);
            }
        }
        
        internal static string brochure_link_text {
            get {
                return ResourceManager.GetString("brochure.link.text", resourceCulture);
            }
        }
        
        internal static string brochure_link_url {
            get {
                return ResourceManager.GetString("brochure.link.url", resourceCulture);
            }
        }
        
        internal static string footer_copyrights {
            get {
                return ResourceManager.GetString("footer.copyrights", resourceCulture);
            }
        }
        
        internal static string home_about_description {
            get {
                return ResourceManager.GetString("home.about.description", resourceCulture);
            }
        }
        
        internal static string home_about_link_text {
            get {
                return ResourceManager.GetString("home.about.link.text", resourceCulture);
            }
        }
        
        internal static string home_about_link_url {
            get {
                return ResourceManager.GetString("home.about.link.url", resourceCulture);
            }
        }
        
        internal static string home_about_subtitle {
            get {
                return ResourceManager.GetString("home.about.subtitle", resourceCulture);
            }
        }
        
        internal static string home_about_title {
            get {
                return ResourceManager.GetString("home.about.title", resourceCulture);
            }
        }
        
        internal static string home_feature_desc_6 {
            get {
                return ResourceManager.GetString("home.feature.desc.6", resourceCulture);
            }
        }
        
        internal static string home_feature_image_6 {
            get {
                return ResourceManager.GetString("home.feature.image.6", resourceCulture);
            }
        }
        
        internal static string home_feature_title_6 {
            get {
                return ResourceManager.GetString("home.feature.title.6", resourceCulture);
            }
        }
        
        internal static string home_features_feature_desc_1 {
            get {
                return ResourceManager.GetString("home.features.feature.desc.1", resourceCulture);
            }
        }
        
        internal static string home_features_feature_desc_2 {
            get {
                return ResourceManager.GetString("home.features.feature.desc.2", resourceCulture);
            }
        }
        
        internal static string home_features_feature_desc_3 {
            get {
                return ResourceManager.GetString("home.features.feature.desc.3", resourceCulture);
            }
        }
        
        internal static string home_features_feature_desc_4 {
            get {
                return ResourceManager.GetString("home.features.feature.desc.4", resourceCulture);
            }
        }
        
        internal static string home_features_feature_desc_5 {
            get {
                return ResourceManager.GetString("home.features.feature.desc.5", resourceCulture);
            }
        }
        
        internal static string home_features_feature_title_1 {
            get {
                return ResourceManager.GetString("home.features.feature.title.1", resourceCulture);
            }
        }
        
        internal static string home_features_feature_title_2 {
            get {
                return ResourceManager.GetString("home.features.feature.title.2", resourceCulture);
            }
        }
        
        internal static string home_features_feature_title_3 {
            get {
                return ResourceManager.GetString("home.features.feature.title.3", resourceCulture);
            }
        }
        
        internal static string home_features_feature_title_4 {
            get {
                return ResourceManager.GetString("home.features.feature.title.4", resourceCulture);
            }
        }
        
        internal static string home_features_feature_title_5 {
            get {
                return ResourceManager.GetString("home.features.feature.title.5", resourceCulture);
            }
        }
        
        internal static string home_features_subtitle {
            get {
                return ResourceManager.GetString("home.features.subtitle", resourceCulture);
            }
        }
        
        internal static string home_features_title {
            get {
                return ResourceManager.GetString("home.features.title", resourceCulture);
            }
        }
        
        internal static string error_message_404 {
            get {
                return ResourceManager.GetString("error.message.404", resourceCulture);
            }
        }
        
        internal static string error_message_500 {
            get {
                return ResourceManager.GetString("error.message.500", resourceCulture);
            }
        }
        
        internal static string page_meta_title_brand {
            get {
                return ResourceManager.GetString("page.meta.title.brand", resourceCulture);
            }
        }
        
        internal static string page_about_title {
            get {
                return ResourceManager.GetString("page.about.title", resourceCulture);
            }
        }
        
        internal static string page_contact_title {
            get {
                return ResourceManager.GetString("page.contact.title", resourceCulture);
            }
        }
        
        internal static string page_about_title2 {
            get {
                return ResourceManager.GetString("page.about.title2", resourceCulture);
            }
        }
        
        internal static string seo_page_about_title {
            get {
                return ResourceManager.GetString("seo.page.about.title", resourceCulture);
            }
        }
        
        internal static string seo_page_about_desc {
            get {
                return ResourceManager.GetString("seo.page.about.desc", resourceCulture);
            }
        }
        
        internal static string seo_page_about_keywords {
            get {
                return ResourceManager.GetString("seo.page.about.keywords", resourceCulture);
            }
        }
        
        internal static string seo_page_contact_title {
            get {
                return ResourceManager.GetString("seo.page.contact.title", resourceCulture);
            }
        }
        
        internal static string seo_page_contact_desc {
            get {
                return ResourceManager.GetString("seo.page.contact.desc", resourceCulture);
            }
        }
        
        internal static string seo_page_contact_keywords {
            get {
                return ResourceManager.GetString("seo.page.contact.keywords", resourceCulture);
            }
        }
    }
}
