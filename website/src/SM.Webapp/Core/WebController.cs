using System.Net;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Catalog.Exceptions;
using SM.Service.Contents.Contracts;
using SM.Webapp.Resources;

namespace SM.Webapp.Core;

//[ServiceFilter(typeof(ExceptionFilter))]
//[ServiceFilter(typeof(GlobalActionFilter))]
public class WebController : Controller
{
    protected readonly IContentService _contentService;
    protected ILogger<WebController> _logger;

    public WebController(IContentService contentService, ILogger<WebController> logger)
    {
        _contentService = contentService;
        _logger = logger;
    }

    protected string CurrentCultureInfo => RouteData.GetLanguage();
    protected string Slug => RouteData.GetSlug();

    protected string PageTitle(string title)
    {
        return title + " | "+ LocalizerRes.page_meta_title_brand;
    }

    protected string PageDescription(string description="")
    {
        return string.IsNullOrEmpty(description) ? LocalizerRes.seo_page_main_description: description;
    }
    protected string PageKeywords(string keywords="")
    {
        return string.IsNullOrEmpty(keywords) ? LocalizerRes.seo_page_main_keywords:keywords;
    }

    [NonAction]
    protected ViewResult ReturnView<T>(Func<T> func, string viewName)
    {
        try
        {
            var funcResult = func();

            return View(viewName, funcResult);
        }
        catch (Exception ex)
        {
            return View(viewName);
        }
    }

    [NonAction]
    protected async Task<ViewResult> ReturnViewAsync<T>(Func<Task<T>> func, string viewName)
    {
        try
        {
            var languages = _contentService.GetLanguages(RouteData.GetLanguage()).Result;
            ViewBag.Languages = languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(LocalizerRes.seo_page_main_title);
            ViewBag.PageDescription = PageDescription(LocalizerRes.seo_page_main_description);
            ViewBag.PageKeywords = PageKeywords(LocalizerRes.seo_page_main_keywords);
            ViewBag.RelCanonicals = languages;

            var funcResult = await func();

            return View(viewName, funcResult);
        }
        catch (Exception ex)
        {
            if (ex.InnerException is NotFoundProductException)
            {
                ViewBag.StatusCode = (int)HttpStatusCode.NotFound;
                ViewBag.ErrorMessage = LocalizerRes.error_message_404;
                HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                _logger.LogError("Exception Url: {url}, Exception Message: {message}",
                    HttpContext.Request.Path, ex.Message);
                return View("Error");
            }
            
            if (ex.InnerException is NotFoundCategoryException)
            {
                ViewBag.StatusCode = (int)HttpStatusCode.NotFound;
                ViewBag.ErrorMessage = LocalizerRes.error_message_404;
                HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                _logger.LogError("Exception Url: {url}, Exception Message: {message}",
                    HttpContext.Request.Path, ex.Message);
                return View("Error");
            }
            
            ViewBag.StatusCode = (int)HttpStatusCode.InternalServerError;
            ViewBag.ErrorMessage = LocalizerRes.error_message_500;
            HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            _logger.LogError("Exception Url: {url}, Exception Message: {message}",
                HttpContext.Request.Path, ex.Message);
            return View("Error");
        }
    }
}