@model SM.Service.Contents.DTOs.GetAboutContentResponse

<div id="content">
    <div class="content-wrap pt-5">
        <!-- Page Title ============================================= -->
        <div class="text-center">
            <h1 class="display-5 mb-1">@Model.Title</h1>
        </div>
        <div class="section mt-0 mb-0 pb-0">
            <div class="container">
                <div class="row align-items-center flex-md-row-reverse col-mb-50 mb-0">
                    <div class="col-lg-6">
                        <img class="box-img shadow-left" src="/images/contents/image-4.jpg" loading="lazy" alt="contents-image-4">
                    </div>
                    <div class="col-lg-6 pe-0 pe-md-5">
                        @Html.Raw(Model.AboutTextPart1)
                    </div>
                </div>

                <div class="clear"></div>

                <div class="row align-items-center mt-0 col-mb-50">
                    <div class="col-lg-6">
                        <img class="box-img" src="/images/contents/image-2.jpg" loading="lazy" alt="contents-image-2">
                    </div>
                    <div class="col-lg-6 ps-md-5 mb-5 mb-md-0">
                        @Html.Raw(Model.AboutTextPart2)
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>