﻿@await Component.InvokeAsync("HomeSlider")

@section Styles
{
    <!-- Plugins/Components CSS -->
    <link rel="stylesheet" href="/css/swiper.css">
}

<!-- Content ============================================= -->
<section id="content">
    <div class="content-wrap pb-1">

        @await Component.InvokeAsync("Brochure")

        @await Component.InvokeAsync("HomeFeatures")

        @await Component.InvokeAsync("HomeAbout")

    </div>
</section><!-- #content end -->