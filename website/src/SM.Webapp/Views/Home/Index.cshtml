@using SM.Webapp.Resources
@await Component.InvokeAsync("HomeSlider")

@section Styles
{
    <!-- Plugins/Components CSS -->
    <link rel="stylesheet" href="/css/swiper.css">
}

@section StructuredData
{
    <!-- Organization Schema for Homepage -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "Organization",
        "name": "@LocalizerRes.page_meta_title_brand",
        "url": "https://www.sabunmutfagi.com",
        "logo": "https://www.sabunmutfagi.com/images/logos/logo.png",
        "description": "@Html.Raw(ViewBag.PageDescription)",
        "foundingDate": "2020",
        "address": {
            "@@type": "PostalAddress",
            "addressCountry": "TR",
            "addressRegion": "Sakarya"
        },
        "contactPoint": {
            "@@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["Turkish", "English", "Russian"]
        },
        "sameAs": [
            "https://www.instagram.com/sabunmutfagi",
            "https://www.facebook.com/sabunmutfagi"
        ]
    }
    </script>
}

<!-- Content ============================================= -->
<section id="content">
    <div class="content-wrap pb-1">

        @await Component.InvokeAsync("Brochure")

        @await Component.InvokeAsync("HomeFeatures")

        @await Component.InvokeAsync("HomeAbout")

    </div>
</section><!-- #content end -->