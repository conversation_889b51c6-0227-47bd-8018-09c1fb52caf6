@using SM.Webapp.Resources
@await Component.InvokeAsync("HomeSlider")

@section Styles
{
    <!-- Plugins/Components CSS -->
    <link rel="stylesheet" href="/css/swiper.css">
}

@section StructuredData
{
    <!-- Organization Schema for Homepage -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "Organization",
        "name": "@Html.Raw(@LocalizerRes.page_meta_title_brand)",
        "url": "https://www.sabunmutfagi.com",
        "logo": "https://www.sabunmutfagi.com/images/logos/logo.png",
        "description": "@Html.Raw(ViewBag.PageDescription)",
        "foundingDate": "2011",
        "address": {
            "@@type": "PostalAddress",
            "addressCountry": "TR",
            "addressRegion": "Sakarya"
        },
        "contactPoint": {
            "@@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["Turkish", "English", "Russian"]
        },
        "sameAs": [
            @if (ViewBag.SocialMediaAccountAddresses != null)
            {
                @for (int i = 0; i < ((IEnumerable<SM.Service.Contents.DTOs.SocialMediaAccountAddressDto>)ViewBag.SocialMediaAccountAddresses).Count(); i++)
                {
                    var socialAccount = ((IEnumerable<SM.Service.Contents.DTOs.SocialMediaAccountAddressDto>)ViewBag.SocialMediaAccountAddresses).ElementAt(i);
                    <text>"@socialAccount.Url"@(i < ((IEnumerable<SM.Service.Contents.DTOs.SocialMediaAccountAddressDto>)ViewBag.SocialMediaAccountAddresses).Count() - 1 ? "," : "")</text>
                }
            }
        ]
    }
    </script>
}

<!-- Content ============================================= -->
<section id="content">
    <div class="content-wrap pb-1">

        @await Component.InvokeAsync("Brochure")

        @await Component.InvokeAsync("HomeFeatures")

        @await Component.InvokeAsync("HomeAbout")

    </div>
</section><!-- #content end -->