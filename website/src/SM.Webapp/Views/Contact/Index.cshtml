@using SM.Webapp.Resources
@model SM.Service.Contents.DTOs.GetContactInfoResponse

@section StructuredData
{
    <!-- ContactPage + LocalBusiness Schema for Contact Page -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "ContactPage",
        "name": "@Html.Raw(ViewBag.PageTitle)",
        "description": "@Html.Raw(ViewBag.PageDescription)",
        "url": "@("https://www.sabunmutfagi.com" + Context.Request.Path)",
        "mainEntity": {
            "@@type": "LocalBusiness",
            "@@id": "https://www.sabunmutfagi.com/#organization",
            "name": "@Html.Raw(@LocalizerRes.page_meta_title_brand)",
            "url": "https://www.sabunmutfagi.com",
            "logo": "https://www.sabunmutfagi.com/assets/images/logo.png",
            "image": "https://www.sabunmutfagi.com/assets/images/logo.png",
            "description": "@Html.Raw(LocalizerRes.seo_page_contact_desc)",
            "foundingDate": "2011",
            "address": {
                "@@type": "PostalAddress",
                "streetAddress": "@Html.Raw(Model.Address)",
                "addressLocality": "Sakarya",
                "addressRegion": "Sakarya",
                "postalCode": "54000",
                "addressCountry": "TR"
            },
            "contactPoint": [
                @if (!string.IsNullOrEmpty(Model.MainPhone))
                {
                    <text>{
                        "@@type": "ContactPoint",
                        "telephone": "@Model.MainPhone",
                        "contactType": "customer service",
                        "availableLanguage": ["Turkish", "English", "Russian"]
                    }</text>
                    @if (!string.IsNullOrEmpty(Model.MainEmail) || (Model.Phones != null && Model.Phones.Any()) || (Model.Emails != null && Model.Emails.Any()))
                    {
                        <text>,</text>
                    }
                }
                @if (!string.IsNullOrEmpty(Model.MainEmail))
                {
                    <text>{
                        "@@type": "ContactPoint",
                        "email": "@Model.MainEmail",
                        "contactType": "customer service",
                        "availableLanguage": ["Turkish", "English", "Russian"]
                    }</text>
                    @if ((Model.Phones != null && Model.Phones.Any()) || (Model.Emails != null && Model.Emails.Any()))
                    {
                        <text>,</text>
                    }
                }
                @if (Model.Phones != null && Model.Phones.Any())
                {
                    @for (int i = 0; i < Model.Phones.Count(); i++)
                    {
                        var phone = Model.Phones.ElementAt(i);
                        <text>{
                            "@@type": "ContactPoint",
                            "telephone": "@phone.Value",
                            "name": "@Html.Raw(phone.Caption)",
                            "contactType": "customer service",
                            "availableLanguage": ["Turkish", "English", "Russian"]
                        }</text>
                        @if (i < Model.Phones.Count() - 1 || (Model.Emails != null && Model.Emails.Any()))
                        {
                            <text>,</text>
                        }
                    }
                }
                @if (Model.Emails != null && Model.Emails.Any())
                {
                    @for (int i = 0; i < Model.Emails.Count(); i++)
                    {
                        var email = Model.Emails.ElementAt(i);
                        <text>{
                            "@@type": "ContactPoint",
                            "email": "@email.Value",
                            "name": "@Html.Raw(email.Caption)",
                            "contactType": "customer service",
                            "availableLanguage": ["Turkish", "English", "Russian"]
                        }</text>
                        @if (i < Model.Emails.Count() - 1)
                        {
                            <text>,</text>
                        }
                    }
                }
            ],
            "openingHoursSpecification": [
                {
                    "@@type": "OpeningHoursSpecification",
                    "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
                    "opens": "10:00",
                    "closes": "18:00"
                }
            ],
            "priceRange": "₺₺",
            "paymentAccepted": [
                "Cash",
                "Credit Card",
                "Debit Card",
                "Bank Transfer",
                "PayPal",
                "Apple Pay",
                "Google Pay",
                "Mastercard",
                "Visa",
                "American Express",
                "Troy",
                "Online Banking"
            ],
            "currenciesAccepted": "TRY",
            "hasOfferCatalog": {
                "@@type": "OfferCatalog",
                "name": "Sabun Mutfağı Ürün Kataloğu",
                "itemListElement": [
                    {
                        "@@type": "Offer",
                        "itemOffered": {
                            "@@type": "Product",
                            "name": "Doğal El Yapımı Sabunlar"
                        },
                        "priceCurrency": "TRY",
                        "priceRange": "25-75",
                        "availability": "https://schema.org/InStock"
                    },
                    {
                        "@@type": "Offer",
                        "itemOffered": {
                            "@@type": "Product",
                            "name": "Organik Cilt Bakım Ürünleri"
                        },
                        "priceCurrency": "TRY",
                        "priceRange": "35-120",
                        "availability": "https://schema.org/InStock"
                    },
                    {
                        "@@type": "Offer",
                        "itemOffered": {
                            "@@type": "Product",
                            "name": "Aromaterapi Ürünleri"
                        },
                        "priceCurrency": "TRY",
                        "priceRange": "45-150",
                        "availability": "https://schema.org/InStock"
                    }
                ]
            },
            "makesOffer": [
                {
                    "@@type": "Offer",
                    "name": "Ücretsiz Kargo",
                    "description": "150 TL ve üzeri alışverişlerde ücretsiz kargo",
                    "priceSpecification": {
                        "@@type": "PriceSpecification",
                        "minPrice": 150,
                        "priceCurrency": "TRY"
                    }
                },
                {
                    "@@type": "Offer",
                    "name": "Hızlı Teslimat",
                    "description": "1-3 iş günü içinde teslimat",
                    "deliveryLeadTime": {
                        "@@type": "QuantitativeValue",
                        "minValue": 1,
                        "maxValue": 3,
                        "unitCode": "DAY"
                    }
                }
            ]
        },
        "breadcrumb": {
            "@@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@@type": "ListItem",
                    "position": 1,
                    "name": "@(LocalizerRes.language == "tr" ? "Ana Sayfa" : LocalizerRes.language == "en" ? "Home" : "Главная")",
                    "item": "https://www.sabunmutfagi.com/@LocalizerRes.language"
                },
                {
                    "@@type": "ListItem",
                    "position": 2,
                    "name": "@(LocalizerRes.language == "tr" ? "İletişim" : LocalizerRes.language == "en" ? "Contact" : "Контакт")",
                    "item": "@("https://www.sabunmutfagi.com" + Context.Request.Path)"
                }
            ]
        }
    }
    </script>
}


<!-- Content
============================================= -->
<div id="content">
    <div class="content-wrap pt-5">

        <div class="container mw-lg">

            <div class="text-center mb-5">
                <h3 class="display-5 mb-5">@LocalizerRes.contact_title</h3>
            </div>

            <div class="row g-5">
                <div class="col-md-7">
                    <div>

                        <div class="form-result"></div>

                        <form id="contact-from" class="mb-0" name="contact-form" role="form" culture="tr" asp-controller="Contact" asp-action="Message" method="post">

                            @*<div class="form-process">
                                <div class="css3-spinner">
                                    <div class="css3-spinner-scaler"></div>
                                </div>
                            </div>*@

                            <div class="row">
                                <div class="col-12 form-group mb-4">
                                    <input type="text" id="template-contactform-name" name="fullName" value="" class="form-control rounded-0 bg-color-2 text-center text-md-start required" placeholder="Your Name">
                                </div>

                                <div class="col-12 form-group mb-4">
                                    <input type="email" id="template-contactform-email" name="email" value="" class="required email form-control rounded-0 bg-color-2 text-center text-md-start" placeholder="Email Address">
                                </div>

                                <div class="col-12 form-group mb-4">
                                    <textarea class="required form-control rounded-0 bg-color-2 text-center text-md-start" id="Message" name="message" rows="6" cols="30" placeholder="Message"></textarea>
                                </div>

                                <div class="col-12 form-group mb-4">
                                    <input type="submit" value="@LocalizerRes.contact_message_sendbutton_text" class="button button-large m-0 bg-color text-light h-op-08"/>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>
                <div class="col-md-5">
                    <div class="grid-inner bg-color bg-opacity-10 p-5 rounded-6">

                        <dl class="row mb-0">
                            <dt class="col-sm-2">
                                <i class="fa-solid fa-location-dot fs-4"></i>
                            </dt>
                            <dd class="col-sm-10">
                                <h4 class="mb-0 fw-normal font-body color">
                                    @Model.Address
                                </h4>
                            </dd>

                            @foreach (var m in Model.Phones)
                            {
                                <dt class="col-sm-2">
                                    <i class="@m.icon fs-5"></i>
                                </dt>
                                <dd class="col-sm-10">
                                    <h4 class="mb-0 fw-normal font-body">
                                        <a href="@m.Value"> @m.Caption</a>
                                    </h4>
                                </dd>
                            }

                            @foreach (var m in Model.Emails)
                            {
                                <dt class="col-sm-2">
                                    <i class="@m.icon fs-5"></i>
                                </dt>
                                <dd class="col-sm-10">
                                    <h4 class="mb-0 fw-normal font-body">
                                        <a href="@m.Value"> @m.Caption</a>
                                    </h4>
                                </dd>
                            }
                         


                            <dt class="col-sm-2">
                                <i class="fa-solid fa-clock fs-5"></i>
                            </dt>
                            <dd class="col-sm-10 mb-0">
                                <h4 class="mb-0 fw-normal font-body"> @LocalizerRes.contact_work_hour<br>10:00 - 18:00 GMT</h4>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="row mt-6">
                <div class="grid-inner rounded-6">
                    <iframe class="google-map " width="100%" height="50" data-skin="gray" data-marker-pos-top="50" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3022.0617084387986!2d30.39501341566124!3d40.76066747932665!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14ccb32275dc0c37%3A0xe94852807fad2cbb!2sSabun%20Mutfagi!5e0!3m2!1sen!2str!4v1667157050704!5m2!1sen!2str"></iframe>
                </div>
            </div>

        </div>

    </div>
</div><!-- #content end -->