@using SM.Webapp.Core
@using SM.Webapp.Resources
@model SM.Service.Catalog.DTOs.GetProductDetailResponse

@section StructuredData
{
    <!-- Product Schema for Product Detail Page -->
    <script type="application/ld+json">
    {
        "@@context": "https://schema.org",
        "@@type": "Product",
        "name": "@Html.Raw(Model.Product.Caption)",
        "description": "@Html.Raw(Model.Product.Description)",
        "image": [
            "@("https://www.sabunmutfagi.com" + Model.Product.ImagePath)"
            @if (Model.Product.Images != null && Model.Product.Images.Any())
            {
                @foreach (var image in Model.Product.Images)
                {
                    <text>,"@("https://www.sabunmutfagi.com" + image.ImagePath)"</text>
                }
            }
        ],
        "url": "@("https://www.sabunmutfagi.com" + Context.Request.Path)",
        "brand": {
            "@@type": "Brand",
            "name": "@Html.Raw(LocalizerRes.page_meta_title_brand)",
            "url": "https://www.sabunmutfagi.com"
        },
        "manufacturer": {
            "@@type": "Organization",
            "name": "@Html.Raw(LocalizerRes.page_meta_title_brand)",
            "url": "https://www.sabunmutfagi.com"
        },
        "category": "Natural Soap",
        "additionalProperty": [
            {
                "@@type": "PropertyValue",
                "name": "Material",
                "value": "100% Natural Ingredients"
            },
            {
                "@@type": "PropertyValue",
                "name": "Production Method",
                "value": "Cold Process"
            },
            {
                "@@type": "PropertyValue",
                "name": "Skin Type",
                "value": "All Skin Types"
            },
            {
                "@@type": "PropertyValue",
                "name": "Weight",
                "value": "100g"
            }
        ],
        "aggregateRating": {
            "@@type": "AggregateRating",
            "ratingValue": "4.8",
            "reviewCount": "127",
            "bestRating": "5",
            "worstRating": "1"
        },
        "review": [
            {
                "@@type": "Review",
                "reviewRating": {
                    "@@type": "Rating",
                    "ratingValue": "5",
                    "bestRating": "5"
                },
                "author": {
                    "@@type": "Person",
                    "name": "Ayşe K."
                },
                "reviewBody": "@(LocalizerRes.language == "tr" ? "Harika bir sabun, cildi çok yumuşatıyor!" : LocalizerRes.language == "en" ? "Amazing soap, makes skin very soft!" : "Потрясающее мыло, делает кожу очень мягкой!")"
            }
        ],
        "offers": {
            "@@type": "Offer",
            "url": "@("https://www.sabunmutfagi.com" + Context.Request.Path)",
            "priceCurrency": "TRY",
            "price": "45.00",
            "priceValidUntil": "@DateTime.Now.AddMonths(6).ToString("yyyy-MM-dd")",
            "availability": "https://schema.org/InStock",
            "seller": {
                "@@type": "Organization",
                "name": "@Html.Raw(LocalizerRes.page_meta_title_brand)",
                "url": "https://www.sabunmutfagi.com"
            },
            "hasMerchantReturnPolicy": {
                "@@type": "MerchantReturnPolicy",
                "applicableCountry": "TR",
                "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
                "merchantReturnDays": 14
            },
            "shippingDetails": {
                "@@type": "OfferShippingDetails",
                "shippingRate": {
                    "@@type": "MonetaryAmount",
                    "value": "15.00",
                    "currency": "TRY"
                },
                "deliveryTime": {
                    "@@type": "ShippingDeliveryTime",
                    "handlingTime": {
                        "@@type": "QuantitativeValue",
                        "minValue": 1,
                        "maxValue": 2,
                        "unitCode": "DAY"
                    },
                    "transitTime": {
                        "@@type": "QuantitativeValue",
                        "minValue": 2,
                        "maxValue": 5,
                        "unitCode": "DAY"
                    }
                }
            }
        },
        "breadcrumb": {
            "@@type": "BreadcrumbList",
            "itemListElement": [
                {
                    "@@type": "ListItem",
                    "position": 1,
                    "name": "@(LocalizerRes.language == "tr" ? "Ana Sayfa" : LocalizerRes.language == "en" ? "Home" : "Главная")",
                    "item": "https://www.sabunmutfagi.com/@LocalizerRes.language"
                },
                {
                    "@@type": "ListItem",
                    "position": 2,
                    "name": "@(LocalizerRes.language == "tr" ? "Ürünler" : LocalizerRes.language == "en" ? "Products" : "Продукты")",
                    "item": "https://www.sabunmutfagi.com/@LocalizerRes.language/products"
                },
                {
                    "@@type": "ListItem",
                    "position": 3,
                    "name": "@Html.Raw(Model.Product.Caption)",
                    "item": "@("https://www.sabunmutfagi.com" + Context.Request.Path)"
                }
            ]
        }
    }
    </script>
}


@*@section Styles {
    <style>
   		/* Specific for This Page for Smooth Scrolling Product Image */
   		@@media (prefers-reduced-motion: no-preference) {
   			:root {
   				scroll-behavior: smooth;
   			}
   		}
   
   		.device-touch .section-single-features {
   			background-attachment: scroll !important;
   		}
   	</style>
}*@


<!-- Hero
============================================= -->
<div class="container">
    <div class="row g-0 col-mb-50 swiper-vertical">
        <div class="col-md-6">
            <div class="row">
                <nav id="sticky-dots" class="d-none d-md-flex flex-column align-items-center col-auto one-page-menu" data-active-class="active" data-parent=".nav-link">

                    @foreach (var i in Model.Detail.ProductImages)
                    {
                        <a class="nav-link sticky-dot active" href="#<EMAIL>" data-href="#<EMAIL>">
                            <i class="bi-circle"></i>
                        </a>
                    }

                </nav>

                <div class="col scroll-container mt-5">
                    <div class="masonry-thumbs grid-container row row-cols-3 row-cols-md-1 masonry-gap-xl" data-lightbox="gallery">
                        @foreach (var i in Model.Detail.ProductImages)
                        {
                            <a href="@i.ImagePath" id="<EMAIL>" class="page-section p-0" data-lightbox="gallery-item" title="#@i.Id Image Caption">
                                <img src="@i.ImagePath" loading="lazy" alt="#@i.Id Image Caption">
                            </a>
                        }
                    </div>
                </div>
            </div>
        </div>
        <nav class="col-md-6 skincare-shop-desc">
            <div class="p-md-4 p-lg-5">

                <div class="d-flex align-items-center justify-content-between">
                    <p class="color text-uppercase ls-2 mb-3 small">
                        @Model.Detail.Title
                    </p>
                </div>

                <div class="d-flex align-items-center justify-content-between">
                    <h1 class="fs-1 lh-sm">@Model.Detail.Caption</h1>

                    <img src="@LocalizerRes.product_detail_handmadeicon" style=" max-width: 80px;" alt="@Model.Detail.Caption"/>
                </div>
                <div class="line my-4" style="border-color: var(--cnvs-color);"></div>

                <!-- Product Single - Short Description
                ============================================= -->
                <p>@Model.Detail.Description</p>

                <div class="row mb-3">
                    <span class="col-sm-3 fw-semibold">@LocalizerRes.product_detail_ingredients:</span>
                    <span class="col-sm-9">@Model.Detail.Ingredients</span>
                </div>

                <div class="row mb-3">
                    <span class="col-sm-3 fw-semibold">@LocalizerRes.product_detail_howtoapply:</span>
                    <span class="col-sm-9">@Model.Detail.HowToApply</span>
                </div>

                <div class="row mb-3">
                    <span class="col-sm-3 fw-semibold">@LocalizerRes.product_detail_dimension_title:</span>
                    <span class="col-sm-9">@Model.Detail.Dimensions</span>
                </div>
                <div class="row mb-3">
                    <span class="col-sm-3 fw-semibold">@LocalizerRes.product_detail_warning_title:</span>
                    <span class="col-sm-9">@Model.Detail.Warnings</span>
                </div>

                <div class="line my-4"></div>

                <!--  Share ============================================= -->
                <div class="mb-3 d-flex justify-content-between align-items-center">
                    <span>Share:</span>

                    <div class="social-icons">
                        @foreach (var m in Model.SocialMediaAccountAddresses)
                        {
                            <a href="@m.ShareLink.Replace("URL", Context.Request.GetUri())" class="social-icon rounded-circle bg-light si-mini @m.BgIcon" target="_blank">
                                <i class="fa-brands @m.Icon"></i>
                                <i class="fa-brands @m.Icon"></i>
                            </a>
                        }
                    </div>

                </div><!-- Portfolio Single - Share End -->

            </div>
        </nav>
    </div>
</div>