﻿@using SM.Webapp.Resources
@using SM.Service.Contents.DTOs
<!DOCTYPE html>
<!--[if lt IE 10]> <html  lang="@LocalizerRes.language" class="iex"> <![endif]-->
<!--[if (gt IE 10)|!(IE)]><!-->
<html lang="@LocalizerRes.language">
<!--<![endif]-->
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@ViewBag.PageTitle</title>
    <link rel="icon" type="image/png" href="/images/icons/favicon.png">
    <meta name="description" content="@Html.Raw(ViewBag.PageDescription)">
    <meta name="keywords" content="@Html.Raw(ViewBag.PageKeywords)">

    @if (ViewBag.RelCanonicals != null)
    {
        @foreach (LanguageDto relCanonical in ViewBag.RelCanonicals)
        {
            if (relCanonical.IsSelected)
            {
                <link rel="canonical" href="@("https://www.sabunmutfagi.com" + relCanonical.Url)">
            }
        }
    }
    <!-- Font Imports -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Domine:wght@400;500;700&family=Roboto:wght@400;500&family=Literata:opsz,wght@7..72,700&display=swap" rel="stylesheet">

    <!-- Core Style -->
    <link rel="stylesheet" href="/css/style-min.css">

    <!-- Font Icons -->
    <link rel="stylesheet" href="/css/font-icons-min.css">

    <!-- Niche Demos -->
    <link rel="stylesheet" href="/css/skincare-min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/custom-min.css">

    @await RenderSectionAsync("Styles", false)

    <!-- Structured Data -->
    @await RenderSectionAsync("StructuredData", false)

</head>
<body class="stretched">

<!-- Document Wrapper
============================================= -->
<div id="wrapper">

    @await Component.InvokeAsync("Header")

    @RenderBody()

    @await Component.InvokeAsync("Footer")
</div><!-- #wrapper end -->

<!-- Go To Top
============================================= -->
<div id="gotoTop" class="uil uil-angle-up rounded-circle" style="left: auto; right: 30px;"></div>

<!-- JavaScripts
============================================= -->
<script src="/js/plugins.min.js"></script>
<script src="/js/functions.bundle-min.js"></script>


@await RenderSectionAsync("Scripts", false)

</body>
</html>