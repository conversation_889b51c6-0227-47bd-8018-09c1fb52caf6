@using SM.Webapp.Resources
<!-- Section About ============================================= -->
<div class="section my-0 pb-0 pt-0">
    <div class="container">
        <div class="row g-0 align-items-md-stretch align-items-lg-center">
            <div class="col-xl-7 col-md-6 img-overlap">
                <img src="/images/contents/image-2.jpg" alt="contents-image-2.jpg" loading="lazy" class="h-100" style="object-fit: cover;">
            </div>
            <div class="col-xl-5 col-md-6 bg-white">
                <div class="position-absolute start-0 top-0 w-100 h-100 bg-color bg-opacity-10"></div>
                <h1 class="display-6">@LocalizerRes.home_about_subtitle</h1>
                <p class="color">
                    @Html.Raw(LocalizerRes.home_about_description)
                </p>
                <a href="@LocalizerRes.home_about_link_url" class="button button-small border-color button-border  m-0 color h-bg-color h-text-light">
                    @LocalizerRes.home_about_link_text
                </a>
            </div>
        </div>
    </div>
</div>