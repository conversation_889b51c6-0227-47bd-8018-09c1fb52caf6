using AspNetCore.Mvc.Routing.Localization.Attributes;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Catalog.Contracts;
using SM.Service.Catalog.DTOs;
using SM.Service.Contents.Contracts;
using SM.Webapp.Core;

namespace SM.WebApp.Controllers;

[LocalizedRoute("en", "catalog")]
[LocalizedRoute("tr", "katalog")]
[LocalizedRoute("ru", "каталог")]
public class CatalogController : WebController
{
    private readonly ICatalogService CatalogService;

    public CatalogController(ILogger<CatalogController> logger, ICatalogService catalogService,
        IContentService contentService) : base(contentService, logger)
    {
        CatalogService = catalogService;
    }

    [LocalizedRoute("en", "products")]
    [LocalizedRoute("tr", "urunler")]
    [LocalizedRoute("ru", "продукты")]
    [ResponseCache(Location = ResponseCacheLocation.Any,Duration =120)]
    public async Task<IActionResult> Products()
    {
        return await ReturnViewAsync(async () =>
        {
            var response = CatalogService.GetProducts(new GetProductsRequest
            {
                CurrentCultureInfo = CurrentCultureInfo,
                Slug = Slug
            }).Result;

            ViewBag.Languages = response.Languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(response.PageContent.SeoTitle);
            ViewBag.PageDescription = PageDescription(response.PageContent.SeoDescription);
            ViewBag.PageKeywords = PageKeywords(response.PageContent.SeoKeywords);
            ViewBag.RelCanonicals = response.Languages;
            return response;
        }, "Products");
    }

    [LocalizedRoute("en", "product-detail")]
    [LocalizedRoute("tr", "urun-detay")]
    [LocalizedRoute("ru", "деталь-изделия")]
    [ResponseCache(Location = ResponseCacheLocation.Any,Duration =120)]
    public async Task<IActionResult> Product()
    {
        return await ReturnViewAsync(async () =>
        {
            var response = CatalogService.GetProductDetail(new GetProductDetailRequest
            {
                CurrentCultureInfo = CurrentCultureInfo,
                Slug = Slug
            }).Result;

            ViewBag.Languages = response.Languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(response.PageContent.SeoTitle);
            ViewBag.PageDescription = PageDescription(response.PageContent.SeoDescription);
            ViewBag.Pagekeywords = PageKeywords(response.PageContent.SeoKeywords);
            ViewBag.RelCanonicals = response.Languages;
            return response;
        }, "Product");
    }
}