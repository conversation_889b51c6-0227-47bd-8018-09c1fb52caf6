using AspNetCore.Mvc.Routing.Localization.Attributes;
using Microsoft.AspNetCore.Mvc;
using SM.Service.Contents.Contracts;
using SM.Service.Message.DTOs;
using SM.Webapp.Core;
using SM.Webapp.Resources;

namespace SM.Webapp.Controllers;

[LocalizedRoute("en", "contact")]
[LocalizedRoute("tr", "iletisim")]
[LocalizedRoute("ru", "cвязь")]
public class ContactController : WebController
{
    private readonly IMessageService _messageService;

    public ContactController(ILogger<ContactController> logger, IContentService contentService,
        IMessageService messageService) : base(contentService, logger)
    {
        _messageService = messageService;
    }

    public async Task<IActionResult> Index()
    {
        return await ReturnViewAsync(async () =>
        {
            var response = _contentService.GetContactInfo(CurrentCultureInfo).Result;
            ViewBag.Languages = response.Languages.prepareLanguageDropdown();
            ViewBag.PageTitle = PageTitle(LocalizerRes.page_contact_title);
            ViewBag.RelCanonicals = response.Languages;
            return response;
        }, "Index");
    }

    [HttpPost]
    [LocalizedRoute("en", "")]
    [LocalizedRoute("tr", "")]
    [LocalizedRoute("ru", "")]
    public IActionResult Message(string fullName, string email, string message)
    {
        var response = _messageService.SendMessage(new SendMessageRequest
        {
            FullName = fullName,
            Email = email,
            Message = message
        }).Result;

        ViewBag.SendMessageIsSuccess = response.IsSuccess;

        return RedirectToAction("Index", "Contact");
    }
}