using SM.Data.Entities.Navigation;

namespace SM.Service.Helper;

public static class Utils
{
    public static string PrepareUrl(NavigationType type, string lang, string slug)
    {
        if (type == NavigationType.In || type == NavigationType.StaticPage) return $"/{lang}{slug}";
        if (type == NavigationType.Category) return $"/{lang}/{GetCategoryLocalizationRoutingData(lang)}/{slug}";

        if (type == NavigationType.Out) return slug;

        return "javascript:void(0)";
    }
    public static string PrepareProductDetailUrl(string langPrefix, string slug, string routing)
    {
        return $"/{langPrefix}/{routing}/{slug}";
    }
    public static string GetCategoryLocalizationRoutingData(string langPrefix)
    {
        var routing = "";
        switch (langPrefix)
        {
            case "tr":
                routing = "katalog/urunler";
                break;
            case "en":
                routing = "catalog/products";
                break;
            case "ru":
                routing = "каталог/продукты";
                break;
            default:
                routing = "katalog/urunler";
                break;
        }

        return routing;
    }
    public static string GetLocalizationRoutingData(string langPrefix)
    {
        var routing = "";
        switch (langPrefix)
        {
            case "tr":
                routing = "katalog/urun-detay";
                break;
            case "en":
                routing = "catalog/product-detail";
                break;
            case "ru":
                routing = "каталог/деталь-изделия";
                break;
            default:
                routing = "katalog/urun-detay";
                break;
        }

        return routing;
    }
    public static int GetIdFormSlug(string slug)
    {
        var list = slug.Split('-');
        return Convert.ToInt32(list[list.Length - 1]);
    }
}